import React, { useState } from 'react';
import { Box, Container } from '@mui/material';
import Dashboard from '../Dashboard';
import Sidebar from '../Sidebar';
import WelcomeCard from '../WelcomeCard';
import { useApp } from '../contexts/AppContext';

function AppLayout() {
  const { state, updateSettings } = useApp();
  const [currentSection, setCurrentSection] = useState('outages');

  const handleNavigation = (section) => {
    setCurrentSection(section);
  };

  const handleThemeChange = () => {
    const newTheme = state.settings.theme === 'light' ? 'dark' : 'light';
    updateSettings({ theme: newTheme });
  };

  const renderContent = () => {
    switch (currentSection) {
      case 'intro':
        return <WelcomeCard />;
      case 'outages':
      case 'checklist':
        return <Dashboard />;
      case 'history':
        return <Box sx={{ p: 3, textAlign: 'center' }}>Historie bude implementována později</Box>;
      case 'settings':
        return <Box sx={{ p: 3, textAlign: 'center' }}>Nastavení bude implementováno později</Box>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: 'background.default'
    }}>
      {/* Sidebar */}
      <Sidebar
        section={currentSection}
        handleNav={handleNavigation}
        mode={state.settings.theme}
        setMode={handleThemeChange}
      />

      {/* Main Content */}
      <Box component="main" sx={{
        flexGrow: 1,
        p: 3,
        ml: { xs: 0, md: '80px' } // Offset for sidebar (80px width)
      }}>
        <Container maxWidth="xl">
          {renderContent()}
        </Container>
      </Box>
    </Box>
  );
}

export default AppLayout;
