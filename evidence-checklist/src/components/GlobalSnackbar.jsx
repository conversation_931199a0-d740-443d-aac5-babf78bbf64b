import React from 'react';
import { Snackbar, Alert } from '@mui/material';
import { useApp } from '../contexts/AppContext';

function GlobalSnackbar() {
  const { state, hideSnackbar } = useApp();

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    hideSnackbar();
  };

  return (
    <Snackbar
      open={state.snackbar.open}
      autoHideDuration={6000}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert 
        onClose={handleClose} 
        severity={state.snackbar.severity}
        variant="filled"
        sx={{ width: '100%' }}
      >
        {state.snackbar.message}
      </Alert>
    </Snackbar>
  );
}

export default GlobalSnackbar;
