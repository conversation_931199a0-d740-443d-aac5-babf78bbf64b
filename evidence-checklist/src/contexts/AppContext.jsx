import React, { createContext, useContext, useState } from "react";

const AppContext = createContext();

export const AppProvider = ({ children }) => {
  const [state, setState] = useState({
    settings: {
      theme: 'light' // Výchozí světl<PERSON> téma
    },
    snackbar: {
      open: false,
      message: '',
      severity: 'info'
    },
    user: '',
    folder: null,
    section: 'intro'
  });

  const updateSettings = (newSettings) => {
    setState(prev => ({
      ...prev,
      settings: { ...prev.settings, ...newSettings }
    }));
  };

  const showSnackbar = (message, severity = 'info') => {
    setState(prev => ({
      ...prev,
      snackbar: { open: true, message, severity }
    }));
  };

  const hideSnackbar = () => {
    setState(prev => ({
      ...prev,
      snackbar: { ...prev.snackbar, open: false }
    }));
  };

  const setUser = (user) => {
    setState(prev => ({ ...prev, user }));
  };

  const setFolder = (folder) => {
    setState(prev => ({ ...prev, folder }));
  };

  const setSection = (section) => {
    setState(prev => ({ ...prev, section }));
  };

  const actions = {
    setUser,
    setFolder,
    setSection,
    showSnackbar,
    hideSnackbar,
    updateSettings
  };

  return (
    <AppContext.Provider value={{
      state,
      setState,
      updateSettings,
      showSnackbar,
      hideSnackbar,
      actions
    }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => useContext(AppContext);