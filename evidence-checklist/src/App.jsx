import React, { useMemo } from 'react'
import './App.css'
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material'
import { AppProvider, useApp } from './contexts/AppContext'
import AppLayout from './components/AppLayout'
import GlobalSnackbar from './components/GlobalSnackbar'

// Definice témat
const createAppTheme = (mode) => createTheme({
  palette: {
    mode,
    primary: { main: '#004C97' },
    secondary: { main: '#00A9CE' },
    success: { main: '#64A70B' },
    error: { main: '#DF4661' },
    warning: { main: '#FFBF3F' },
    info: { main: '#B76CA4' },
    text: { primary: '#004C97', secondary: '#616265' },
    background: {
      default: mode === 'light' ? '#f7fafd' : '#1a2233',
      paper: mode === 'light' ? '#ffffff' : '#2d3748'
    },
  },
  typography: {
    fontFamily: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif',
    h1: { fontWeight: 700 },
    h2: { fontWeight: 700 },
    h3: { fontWeight: 700 },
    h4: { fontWeight: 700 },
    h5: { fontWeight: 700 },
    h6: { fontWeight: 700 },
    button: { textTransform: 'none', fontWeight: 600 },
  },
  shape: { borderRadius: 10 },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 20px 0 rgba(0,76,151,0.1)',
          borderRadius: 12,
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
        }
      }
    }
  }
})

// Komponenta pro poskytování tématu
function ThemeWrapper({ children, mode }) {
  const theme = useMemo(() => createAppTheme(mode), [mode])

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  )
}

function App() {

  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  )
}

// Hlavní obsah aplikace s přístupem ke kontextu
function AppContent() {
  const { state } = useApp()

  return (
    <ThemeWrapper mode={state.settings.theme}>
      <AppLayout />
      <GlobalSnackbar />
    </ThemeWrapper>
  )
}

export default App
