import jsPDF from 'jspdf';

export const exportService = {
  /**
   * Export checklistu do PDF
   */
  async exportChecklistToPDF(checklistData, outage, user) {
    try {
      const doc = new jsPDF();
      
      // Nastavení fontu
      doc.setFont('helvetica');
      
      // Hlavička
      doc.setFontSize(20);
      doc.setTextColor(0, 76, 151); // OTE modrá
      doc.text('OTE - Checklist pro odstávku', 20, 30);
      
      // Informace o odstávce
      doc.setFontSize(12);
      doc.setTextColor(0, 0, 0);
      let yPosition = 50;
      
      if (outage) {
        doc.text(`Odstávka: ${outage.name || 'Neznámá'}`, 20, yPosition);
        yPosition += 10;
        doc.text(`Datum: ${outage.date || 'Neznámé'}`, 20, yPosition);
        yPosition += 10;
        doc.text(`Čas: ${outage.time || 'Neznámý'}`, 20, yPosition);
        yPosition += 10;
      }
      
      if (user) {
        doc.text(`Kontrolující: ${user}`, 20, yPosition);
        yPosition += 10;
      }
      
      doc.text(`Datum exportu: ${new Date().toLocaleDateString('cs-CZ')}`, 20, yPosition);
      yPosition += 20;
      
      // Checklist položky
      doc.setFontSize(14);
      doc.text('Checklist položky:', 20, yPosition);
      yPosition += 15;
      
      doc.setFontSize(10);
      
      if (checklistData.checklist && checklistData.checklist.length > 0) {
        checklistData.checklist.forEach((item, index) => {
          // Kontrola, zda se vejdeme na stránku
          if (yPosition > 270) {
            doc.addPage();
            yPosition = 20;
          }
          
          const status = item.checked ? '✓' : '✗';
          const statusColor = item.checked ? [100, 167, 11] : [223, 70, 97]; // Zelená/Červená
          
          doc.setTextColor(...statusColor);
          doc.text(status, 20, yPosition);
          
          doc.setTextColor(0, 0, 0);
          doc.text(`${index + 1}. ${item.text}`, 30, yPosition);
          
          if (item.note) {
            yPosition += 7;
            doc.setFontSize(8);
            doc.setTextColor(100, 100, 100);
            doc.text(`Poznámka: ${item.note}`, 35, yPosition);
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
          }
          
          yPosition += 12;
        });
      }
      
      // Statistiky
      yPosition += 10;
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }
      
      doc.setFontSize(12);
      doc.text('Statistiky:', 20, yPosition);
      yPosition += 15;
      
      doc.setFontSize(10);
      const totalItems = checklistData.checklist?.length || 0;
      const completedItems = checklistData.checklist?.filter(item => item.checked).length || 0;
      const completionRate = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
      
      doc.text(`Celkem položek: ${totalItems}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Dokončeno: ${completedItems}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Míra dokončení: ${completionRate}%`, 20, yPosition);
      
      // Uložení
      const fileName = `ote-checklist-${outage?.name || 'unknown'}-${new Date().toISOString().slice(0, 10)}.pdf`;
      doc.save(fileName);
      
      return true;
    } catch (error) {
      console.error('Chyba při exportu do PDF:', error);
      throw error;
    }
  },

  /**
   * Export do JSON (fallback)
   */
  exportToJSON(data, fileName) {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${fileName}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      return true;
    } catch (error) {
      console.error('Chyba při exportu do JSON:', error);
      throw error;
    }
  },

  /**
   * Export do Excel
   */
  exportToExcel(data, fileName) {
    try {
      // Implementace pro Excel export pomocí xlsx knihovny
      // Toto by vyžadovalo import xlsx knihovny
      console.log('Excel export není zatím implementován');
      throw new Error('Excel export není zatím implementován');
    } catch (error) {
      console.error('Chyba při exportu do Excel:', error);
      throw error;
    }
  }
};
